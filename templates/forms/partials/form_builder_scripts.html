{% load static %}
{% load vite_tags %}

<!-- Vue App Mount Point -->
<div id="form-builder-app" class="form-builder-container"></div>

<!-- Form Builder Data Setup -->
<script id="form-fields-json" type="application/json">
{{ form_fields_json|safe }}
</script>
<script id="form-settings-json" type="application/json">
{{ form_settings_json|safe }}
</script>
<script id="form-customization-json" type="application/json">
{{ form_customization_json|safe }}
</script>

<script>
  console.log('=== FORM BUILDER INITIALIZATION ===')
  
  function safeParseJSON(id, fallback = {}) {
    try {
      return JSON.parse(document.getElementById(id).textContent)
    } catch (err) {
      console.warn(`Failed to parse JSON from ${id}:`, err)
      return fallback
    }
  }
  
  // Parse and initialize formBuilderData
  const fieldsData = safeParseJSON('form-fields-json', [])
  const settingsData = safeParseJSON('form-settings-json', {
    allowMultipleSubmissions: false,
    requireLogin: false,
    collectEmail: false,
    successMessage: 'Thank you for your submission!',
    redirectUrl: ''
  })
  
  const customizationData = safeParseJSON('form-customization-json', {
    theme: 'default',
    colors: { primary: '#3B82F6' },
    layout: { width: 'medium', spacing: 'normal' }
  })
  
  window.formBuilderData = {
    formData: {
      id: '{{ form.id }}',
      name: '{{ form.name|escapejs }}',
      description: '{{ form.description|escapejs }}',
      status: '{{ form.status|escapejs }}',
      fields: fieldsData,
      settings: settingsData,
      customization: customizationData
    },
    csrfToken: '{{ csrf_token }}',
    apiBaseUrl: '/api/v1',
    formSlug: '{{ form.slug }}'
  }
  
  console.log('Form Builder Data Setup Complete:', window.formBuilderData)
</script>

<!-- Load Vite Assets (JS & CSS) -->
{% load_vite_assets 'form-builder' %}

<!-- Vue App Bootstrap -->
<script>
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded - checking for initFormBuilder()...')
  
    if (typeof window.initFormBuilder === 'function') {
      try {
        console.log('Initializing Vue form builder app...')
        window.initFormBuilder() // Mount Vue app
      } catch (e) {
        console.error('Error running initFormBuilder:', e)
      }
    } else {
      console.warn('initFormBuilder is not defined. Did Vite load correctly?')
    }
  })
</script>

<!-- Form Builder Styles -->
<style>
  .form-builder-container {
    min-height: 100vh;
    padding: 2rem;
    background: #f9fafb;
  }
  
  .field-type-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .selected-field {
    border-color: #3b82f6 !important;
    background-color: #ebf8ff !important;
  }
</style>
